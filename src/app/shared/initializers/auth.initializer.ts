import { inject } from '@angular/core';
import { AuthService } from '../services/auth.service';

/**
 * Authentication initializer that runs on app startup
 * Restores user authentication state from stored tokens
 * This ensures logged-in state is maintained when opening new tabs
 */
export function authInitializer(): () => Promise<boolean> {
  return () => {
    const authService = inject(AuthService);
    
    return new Promise((resolve) => {
      authService.initializeAuthState().subscribe({
        next: (isAuthenticated) => {
          // Always resolve, even if authentication fails
          // We don't want to block app startup
          resolve(isAuthenticated);
        },
        error: () => {
          // Silently handle errors and continue app startup
          resolve(false);
        }
      });
    });
  };
}
